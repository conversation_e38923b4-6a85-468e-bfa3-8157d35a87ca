upstream backend {
    server localhost:3000;
}

server {

    listen 80;
    listen [::]:80;
    server_name domain.com www.domain.com;
    return 301 https://domain.com$request_uri;
}

server {

    listen 443 ssl;
    server_name www.domain.com;
    ssl_certificate /etc/letsencrypt/live/domain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/domain.com/privkey.pem;
    return 301 https://domain.com$request_uri;
}

server {

    listen 443 ssl;

    root /var/www/aeduca/client;
    server_name domain.com;
    index index.html;

    error_page 502 503 @maintenance;

    charset utf-8;

    location / {
        try_files $uri @backend;
    }

    location @backend {
        proxy_pass http://backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        proxy_redirect off;
    }

    location @maintenance {
        root /var/www/maintenance;
        try_files $uri /index.html =503;
    }

    ssl_certificate /etc/letsencrypt/live/domain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/domain.com/privkey.pem;
    include /etc/letsencrypt/options-ssl-nginx.conf;
    ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem;
}
