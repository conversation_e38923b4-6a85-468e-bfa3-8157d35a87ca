# Dockerized project

Dockerize any project, put laravel project inside dockerized, call "src", read more below

## Usage

To get started, make sure you have [Docker installed](https://docs.docker.com/docker-for-mac/install/) on your system, and then clone this repository.

Next, navigate in your terminal to the directory you cloned this, and spin up the containers for the web server by running `docker-compose up -d --build app`.

After that completes, follow the steps from the [src/README.md](src/README.md) file to get your Laravel project added in (or create a new blank one).

**Note**: Your Postgres database host name should be `postgres`, **not** `localhost`. The username and database should both be `homestead` with a password of `secret`.

Bringing up the Docker Compose network with `app` instead of just using `up`, ensures that only our site's containers are brought up at the start, instead of all of the command containers as well. The following are built for our web server, with their exposed ports detailed:

- **nginx** - `:80`
- **pgsql** - `:5432`
- **php** - `:9000`
- **redis** - `:6379`

Three additional containers are included that handle Composer, NPM, and Artisan commands _without_ having to have these platforms installed on your local computer. Use the following command examples from your project root, modifying them to fit your particular use case.

## Some Issues

If you encounter any issues with filesystem permissions while visiting your application or running a container command, try completing one of the sets of steps below.

**If you are using your server or local environment as a user that is not root:**

- Bring any container(s) down with `docker-compose down`
- In your terminal, run `export UID=$(id -u)` and then `export GID=$(id -g)`
- If you see any errors about readonly variables from the above step, you can ignore them and continue
- Re-build the containers by running `docker-compose build --no-cache`

Then, either bring back up your container network or re-run the command you were trying before, and see if that fixes it.

## Persistent Postgres Storage

By default, whenever you bring down the Docker network, your Postgres data will be removed after the containers are destroyed. If you would like to have persistent data that remains after bringing containers down and back up, do the following:

1. Create a `pgsql` folder in the project root, alongside the `nginx` and `src` folders.
2. Under the pgsql service in your `docker-compose.yml` file, add the following lines:

```
volumes:
  - ./docker/data:/var/lib/postgresql
```

## Usage in Development

First, run the following commands to install your dependencies and start the dev server:

- `sudo usermod -aG docker $USER`
- `docker rm $(docker ps --filter status=exited -q)`
- `sudo systemctl status docker docker.socket`
- `docker system prune -a -y`
- `docker-compose up -d`
- `docker-compose run --rm composer install`
- `docker-compose run --rm artisan key:generate`
- `docker-compose run --rm artisan jwt:secret`
- `docker-compose run --rm npm install`
- `make start`
- `make check`
- `make run`

After that, you should be able to use `@vite` directives to enable hot-module reloading on your local Laravel application.

## Usage in Production

While I originally created this template for local development, it's robust enough to be used in basic Laravel application deployments. The biggest recommendation would be to ensure that HTTPS is enabled by making additions to the `nginx/default.conf` file and utilizing something like [Let's Encrypt](https://hub.docker.com/r/linuxserver/letsencrypt) to produce an SSL certificate.

Want to build for production? Simply run `docker-compose run --rm npm run build`.
