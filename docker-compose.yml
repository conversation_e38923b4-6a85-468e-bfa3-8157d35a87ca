networks:
  aeduca:

services:
  app:
    container_name: ${APP_NAME}_app
    depends_on:
      - redis
      - postgres
    ports:
      - "9000:9000"
    build:
      context: ./docker
      dockerfile: dockerfiles/app.dockerfile
      args:
        - UID=${UID:-1000}
        - GID=${GID:-1000}
    volumes:
      - ./src:/var/www/html:delegated
    networks:
      - aeduca

  nginx:
    container_name: ${APP_NAME}_nginx
    build:
      context: ./docker
      dockerfile: dockerfiles/nginx.dockerfile
      args:
        - UID=${UID:-1000}
        - GID=${GID:-1000}
    ports:
      - "${NGINX_PORT_80:-80}:80"
      - "${NGINX_PORT_443:-443}:443"
    depends_on:
      - app
    volumes:
      - ./src:/var/www/html:delegated
    networks:
      - aeduca

  composer:
    container_name: ${APP_NAME}_composer
    build:
      context: ./docker
      dockerfile: dockerfiles/app.dockerfile
      args:
        - UID=${UID:-1000}
        - GID=${GID:-1000}
    volumes:
      - ./src:/var/www/html
    depends_on:
      - app
    entrypoint: ["composer"]
    networks:
      - aeduca

  artisan:
    container_name: ${APP_NAME}_artisan
    build:
      context: ./docker
      dockerfile: dockerfiles/app.dockerfile
      args:
        - UID=${UID:-1000}
        - GID=${GID:-1000}
    volumes:
      - ./src:/var/www/html:delegated
    depends_on:
      - app
    entrypoint: ["php", "/var/www/html/artisan"]
    networks:
      - aeduca

  npm:
    image: node:current-alpine
    container_name: ${APP_NAME}_npm
    volumes:
      - ./src:/var/www/html
    ports:
      - "5173:5173"
    working_dir: /var/www/html
    entrypoint: ["npm"]
    networks:
      - aeduca

  postgres:
    container_name: ${APP_NAME}_postgres
    build:
      context: ./docker
      dockerfile: dockerfiles/postgres.dockerfile
    volumes:
      - "./docker/data/postgres:/var/lib/postgresql/data"
    environment:
      POSTGRES_USER: ${DB_USERNAME}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
      POSTGRES_DB: ${DB_DATABASE}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=es_PE.utf8 --lc-ctype=es_PE.utf8"
    ports:
      - "5432:5432"
    networks:
      - aeduca

  redis:
    container_name: ${APP_NAME}_redis
    image: redis:alpine
    ports:
      - "${REDIS_PORT:-6379}:6379"
    command: "redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}"
    volumes:
      - "./docker/data/redis:/data"
    networks:
      - aeduca
