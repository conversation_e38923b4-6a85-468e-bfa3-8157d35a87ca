start:
	sudo systemctl start docker; \
	docker-compose up -d; \
		echo "Started successfully, <PERSON>! 😍"

stop:
	sudo systemctl stop docker docker.socket; \
	docker-compose down; \
		echo "I stoped your project, <PERSON>! 😍"

prettier:
	docker-compose run --rm npm run prettier; \
		echo "Your code is beautiful now! 😍"

lint:
	docker-compose run --rm npm run lint; \
		echo "Hey, you have some lint errors! 😍"

check:
	docker-compose run --rm npm run check; \
		echo "Hey, you have some check results! 😍"

run:
	docker-compose run --rm --service-ports npm run dev; \
		echo "Your app is stoped! 😍"

hello:
	echo "Hey there, I am dev! 😍"

merge:
	git fetch origin main; \
	git merge origin/main; \
		echo "Merged successfully! 😍"

push:
	@read -p "Elias, enter your commit message: " name; \
		git add .; \
		git commit -m "$$name"; \
		git push origin main; \
			echo "Yeap Elias your code has been pushed! i love u😍"