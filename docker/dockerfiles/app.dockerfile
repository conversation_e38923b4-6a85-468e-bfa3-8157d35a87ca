FROM php:8.3-fpm-alpine AS app

ARG UID
ARG GID

ENV UID=${UID}
ENV GID=${GID}
ENV USER=elias

RUN mkdir -p /var/www/html

WORKDIR /var/www/html

COPY --from=composer:latest /usr/bin/composer /usr/local/bin/composer

RUN addgroup -g ${GID} --system ${USER}
RUN adduser -G ${USER} --system -D -s /bin/sh -u ${UID} ${USER}

RUN set -ex \
    && apk --no-cache add libpng-dev libzip-dev postgresql-dev \
    && docker-php-ext-install gd zip pdo pdo_pgsql

RUN sed -i "s/user = www-data/user = ${USER}/g" /usr/local/etc/php-fpm.d/www.conf
RUN sed -i "s/group = www-data/group = ${USER}/g" /usr/local/etc/php-fpm.d/www.conf
RUN echo "php_admin_flag[log_errors] = on" >> /usr/local/etc/php-fpm.d/www.conf

USER ${USER}

EXPOSE 9000

CMD ["php-fpm"]
